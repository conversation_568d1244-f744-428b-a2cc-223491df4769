const { log: logger } = require("./utils/logger");
const { getDataFromS3 } = require("./utils/aws");
const logsService = require("./service/logs");
const { getStaticData, getStaticSourcesArray } = require("./utils/staticData");
const { getClient } = require("./utils/connection");
const { userService } = require("./service/users")

const MANUAL_ENTRY_DEVICE_ID = -1;
const BGM_TRACKER_ID = 7;
const CGM_TRACKER_ID = 8;

const Bottleneck = require('bottleneck');
// Initialize limiter: adjust maxConcurrent/minTime based on system capacity
const limiter = new Bottleneck({
  maxConcurrent: 5,
  minTime: 100
});

const defaultHeaders = {
  "Access-Control-Allow-Headers":
    "Origin, X-Requested-With, Content-Type, Accept, X-Access-Token, x-api-key, X-Install-Type",
  "Access-Control-Allow-Origin": "*",
};

exports.handler = async (event) => {
  const body = JSON.parse(event.Records[0].body);
  logger.info(`Received event for user ${body.userId} with S3 key ${body.s3Key}`);
  const { userId, s3Key } = body;
  if (!userId || !s3Key) {
    return {
      statusCode: 400,
      body: JSON.stringify({
        success: false,
        message: `Invalid event data`
      }),
      headers: defaultHeaders,
    };
  }

  try {
    const data = await getDataFromS3(s3Key);
    await getStaticData();
    await getClient();

    console.time(`processLogs`)
    const response = await processLogs(userId, data);
    console.timeEnd(`processLogs`)

    return {
      statusCode: 200,
      body: JSON.stringify({
        success: true,
        message: `Request processed successfully`,
        data: response,
      }),
      headers: defaultHeaders,
    };
  } catch (err) {
    logger.error(`Error in TrackersIngestion lambda for user ${userId}, S3 key ${s3Key}: ${err.message}\nStack: ${err.stack || 'No stack trace available'}`);
    return {
      statusCode: 500,
      body: JSON.stringify({
        success: false,
        message: `Failed to process request`,
      }),
      headers: defaultHeaders,
    };
  }
};

async function processLogs(userId, logsData) {
  const staticSourcesData = await getStaticSourcesArray();
  const response = {};
  const trackerIdLogsMapping = {};
  const sourceIdNameMapping = {};
  const CGMInsertedLogIds = [];
  const trackerEntries = Object.entries(logsData);

  // Log summary of what we're processing
  const trackerSummary = trackerEntries.map(([trackerId, logs]) => `${trackerId}: ${logs.length} logs`).join(', ');
  logger.info(`Processing logs for user ${userId}: ${trackerSummary}`);

  // Process tracker-wise in parallel
  await Promise.all(trackerEntries.map(async ([trackerId, logsByTracker]) => {
    const tracker = logsService.trackerMap[trackerId];
    response[trackerId] = [];

    // Prepare documents for batch processing
    const docsWithMetadata = await Promise.all(logsByTracker.map(async (newDoc) => {
      const logIdExists = newDoc.logId || null;
      const source = staticSourcesData.find((s) => s.id == newDoc.sourceId);

      // Track source info for last synced values
      sourceIdNameMapping[newDoc.sourceId] = {
        sourceName: source.name,
        deviceId: newDoc.deviceId,
      };

      return {
        doc: newDoc,
        isIncremental: source.increamentalData,
        logIdExists
      };
    }));

    // Group documents by whether they need special handling
    const incrementalDocsWithoutLogId = docsWithMetadata.filter(item =>
      item.isIncremental && !item.logIdExists
    );

    const standardDocs = docsWithMetadata.filter(item =>
      !item.isIncremental || item.logIdExists
    );

    // Process incremental docs that need timestamp lookup
    const incrementalResults = [];

    // Process in smaller batches to avoid overwhelming the system
    const BATCH_SIZE = 50;
    const incrementalBatchCount = Math.ceil(incrementalDocsWithoutLogId.length / BATCH_SIZE);

    if (incrementalDocsWithoutLogId.length > 0) {
      logger.info(`Processing ${incrementalDocsWithoutLogId.length} incremental docs for tracker ${trackerId} in ${incrementalBatchCount} batches`);
    }

    for (let i = 0; i < incrementalDocsWithoutLogId.length; i += BATCH_SIZE) {
      const batch = incrementalDocsWithoutLogId.slice(i, i + BATCH_SIZE);

      // Process each doc in the batch with limiter
      const batchResults = await Promise.all(batch.map(({ doc }) =>
        limiter.schedule(async () => {
          const existingLog = await logsService.getLogByTimestampByDeviceId(
            userId,
            tracker.indexName,
            doc.timestamp,
            doc.deviceId
          );

          let id;
          if (existingLog) {
            id = await logsService.updateLogById(trackerId, tracker.indexName, doc, userId, existingLog.id);
          } else {
            id = await logsService.insertData(trackerId, tracker.indexName, doc);
          }

          // Track for response and post-processing
          trackerIdLogsMapping[trackerId] ||= [];
          trackerIdLogsMapping[trackerId].push(doc);

          return id;
        })
      ));

      incrementalResults.push(...batchResults);
    }

    // Process standard docs with bulk operations
    let standardResults = [];
    if (standardDocs.length > 0) {
      logger.info(`Processing ${standardDocs.length} standard docs for tracker ${trackerId} using bulk upsert`);
      const docsForBulkUpsert = standardDocs.map(item => item.doc);

      // Use bulk upsert for standard docs
      const bulkResults = await logsService.bulkUpsertData(
        trackerId,
        tracker.indexName,
        docsForBulkUpsert
      );

      // Convert result map to array of IDs in the original order
      standardResults = standardDocs.map((_, index) => bulkResults[index]);

      // Track for post-processing
      trackerIdLogsMapping[trackerId] ||= [];
      trackerIdLogsMapping[trackerId].push(...docsForBulkUpsert);
    }

    // Combine results and add to response
    const combinedResults = [...incrementalResults, ...standardResults].filter(Boolean);
    logger.info(`Completed processing for tracker ${trackerId}: ${combinedResults.length} total logs processed (${incrementalResults.length} incremental, ${standardResults.length} standard)`);
    response[trackerId] = combinedResults;

    // Handle BGM to CGM conversion
    if (Number(trackerId) === BGM_TRACKER_ID) {
      const { indexName: cgmIndexName } = logsService.trackerMap[CGM_TRACKER_ID];
      const bgmDocs = [...incrementalDocsWithoutLogId, ...standardDocs].map(item => item.doc);
      logger.info(`Converting ${bgmDocs.length} BGM logs to CGM format`);

      // Use bulk upsert for CGM docs
      const cgmResults = await logsService.bulkUpsertData(
        CGM_TRACKER_ID,
        cgmIndexName,
        bgmDocs
      );

      // Extract IDs from results
      const cgmIds = Object.values(cgmResults).filter(Boolean);
      logger.info(`Successfully converted ${cgmIds.length} BGM logs to CGM format`);
      CGMInsertedLogIds.push(...cgmIds);

      // Track for post-processing
      trackerIdLogsMapping[CGM_TRACKER_ID] ||= [];
      trackerIdLogsMapping[CGM_TRACKER_ID].push(...bgmDocs);
    }
  }));

  // Add CGM IDs to response if any
  if (trackerIdLogsMapping[CGM_TRACKER_ID]?.length > 0) {
    logger.info(`Adding ${CGMInsertedLogIds.length} CGM logs to response`);
    response[CGM_TRACKER_ID] = CGMInsertedLogIds;
  }

  // Post-processing
  const computeTargetAchievementFlag = await logsService.postLogUpsert(userId, trackerIdLogsMapping);
  const trackerCount = Object.keys(trackerIdLogsMapping).length;
  logger.info(`Post log upsert completed for user ${userId}: ${trackerCount} trackers processed, target computation ${computeTargetAchievementFlag ? 'succeeded' : 'failed'}`);

  const sourceCount = Object.keys(sourceIdNameMapping).length;
  const sources = Object.keys(sourceIdNameMapping);
  logger.info(`Updating last synced values for user ${userId}: ${sourceCount} sources [${sources.join(', ')}]`);
  await updateLastSyncedValues(userId, sourceIdNameMapping);

  // Log summary of what was processed
  const responseSummary = Object.entries(response)
    .map(([trackerId, ids]) => `${trackerId}: ${ids.length} logs`)
    .join(', ');
  logger.info(`Completed processing logs for user ${userId}: ${responseSummary}`);

  return response;
}

async function updateLastSyncedValues(userId, sourceIdNameMapping) {
  const currentDateTime = new Date().toISOString();
  for (const [sourceId, sourceData] of Object.entries(sourceIdNameMapping)) {
    if(sourceData.deviceId) {
      const doc = {
        source: sourceData.sourceName,
        sourceId: Number(sourceId),
        createdAt: currentDateTime,
        updatedAt: currentDateTime,
        userId,
        devices: { [sourceData.deviceId]: { lastSynced: currentDateTime } },
        lastSynced: currentDateTime,
      };
      await userService.upsertUserDetails(userId, doc, sourceId);
    }
  }
}


