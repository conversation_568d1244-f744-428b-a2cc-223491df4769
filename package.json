{"name": "enqueue", "version": "1.0.0", "description": "First lightweight Lambda in trackers async pipeline", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "node test/test.js", "start": "node index.js", "start:dev": "NODE_ENV=development node index.js", "start:sandbox": "NODE_ENV=sandbox node index.js", "start:prod": "NODE_ENV=production node index.js", "deploy:dev": "NODE_ENV=development ./scripts/upload-env-to-secrets.sh development", "deploy:sandbox": "NODE_ENV=sandbox ./scripts/upload-env-to-secrets.sh sandbox", "deploy:prod": "NODE_ENV=production ./scripts/upload-env-to-secrets.sh production"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-lambda": "^3.821.0", "@aws-sdk/client-s3": "^3.821.0", "@aws-sdk/client-sqs": "^3.821.0", "@aws-sdk/credential-provider-node": "^3.787.0", "@opensearch-project/opensearch": "^3.5.1", "aws-sdk": "^2.1692.0", "axios": "^1.8.4", "bottleneck": "^2.19.5", "bunyan": "^1.8.15", "bunyan-format": "^0.2.1", "dotenv": "^16.5.0", "lambda-local": "^2.2.0", "lodash": "^4.17.21", "nconf": "^0.12.1", "package.json": "^2.0.1"}}