const axios = require("axios").default;
const { config } = require("../environment/index");
const { log: logger } = require("./logger");

const baseUrl = `https://services.dev.healthtechgate.com/mindfulness/api/v1`;

async function addLog(userId, doc) {
  try {
    const xAPIKey = 'sqi9oXyvQa4btGV24j1Jw9MSkHpcOvCo9BBY7eEZ';
    const reqConfig = {
      "Content-Type": "application/json",
      headers: { "x-api-key": xAPIKey },
    };

    const mindfulnessId = -1;
    const reqBody = {
      [mindfulnessId]: {
        mindfulnessName: doc.name,
        totalDuration: doc?.duration || 0, // will be directly in seconds
        timestamp: doc.timestamp
      },
    };

    const response = await axios.post(`${baseUrl}/mindfulness/logs?user_guid=${userId}`, reqBody, reqConfig);
    const logId = response.data.data?.[0];
    return logId;
  } catch (error) {
    logger.warn(JSON.stringify(error));
    return null;
  }
}

module.exports = { addLog };
