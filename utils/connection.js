const { Client, Connection } = require("@opensearch-project/opensearch");
const { defaultProvider } = require("@aws-sdk/credential-provider-node");
const aws4 = require("aws4");
const { config } = require("../environment/index");

const { log: logger } = require("./logger");

let openSearchClientInstance;
let clientInitializationPromise = null;

// Function to create a custom connection that signs requests with AWS4
function createAwsConnector(credentials, region) {
  class AmazonConnection extends Connection {
    buildRequestObject(params) {
      const request = super.buildRequestObject(params);
      request.service = "es"; // OpenSearch service identifier
      request.region = region;
      request.headers = request.headers || {};
      // The host header is typically set by the aws4 library or the HTTP agent.
      // The OpenSearch client sets `request.hostname`. aws4.sign expects `request.host` or `request.hostname`.
      if (!request.headers["host"]) {
          request.headers["host"] = request.hostname;
      }

      // Clean up undefined headers that aws<PERSON> might not like
      for (const key in request.headers) {
        if (request.headers[key] === undefined) {
          delete request.headers[key];
        }
      }
      // Sign the request using AWS4
      // The aws4 library mutates the request object by adding auth headers
      return aws4.sign(request, credentials);
    }
  }
  return {
    Connection: AmazonConnection,
  };
}

// Function to create the OpenSearch client instance
async function getClienInternal() {
  const initStartTime = Date.now();
  logger.info('Starting OpenSearch client creation process...');

  const awsRegion = config.REGION || process.env.AWS_REGION; // process.env.AWS_REGION is standard in Lambda
  if (!awsRegion) {
    const errMsg = 'AWS Region is not configured. Ensure AWS_REGION environment variable is set.';
    logger.error(errMsg);
    throw new Error(errMsg);
  }
  if (!config.OS_HOST || config.OS_HOST.includes('your-opensearch-domain-endpoint')) {
     const errMsg = 'OpenSearch host (OPENSEARCH_ENDPOINT) is not configured or is using the placeholder value.';
     logger.error(errMsg);
     throw new Error(errMsg);
  }

  logger.info(`Using AWS Region: ${awsRegion}`);
  logger.info(`Attempting to connect to OpenSearch Host: ${config.OS_HOST}`);


  let credentials;
  try {
    const credentialsStartTime = Date.now();
    logger.info('Starting AWS credential resolution using defaultProvider.');
    // defaultProvider will automatically use the Lambda execution role credentials
    credentials = await defaultProvider({
      timeout: 5000, // 5-second timeout for credential fetching
      maxRetries: 2,   // Reduce retries in Lambda
    })();
    const credentialsDuration = Date.now() - credentialsStartTime;
    logger.info(`AWS credentials resolved in ${credentialsDuration}ms. AWS Access Key ID: ${credentials.accessKeyId ? 'Loaded' : 'Not Loaded'}`);
  } catch (error) {
    logger.error('Error resolving AWS credentials:', error);
    throw error; // Re-throw to be caught by the caller or clientInitializationPromise
  }

  const clientStartTime = Date.now();
  logger.info('Creating OpenSearch client with AWS4 signing connector.');

  try {
    const client = new Client({
      ...createAwsConnector(credentials, awsRegion),
      node: config.OS_HOST, // Your OpenSearch domain endpoint
      requestTimeout: config.OS_REQUEST_TIMEOUT,
      pingTimeout: config.OS_PING_TIMEOUT,
      maxRetries: config.OS_MAX_RETRIES,
      compression: 'gzip', // Enable gzip compression for requests and responses
      // Ensure keep-alive is enabled for Lambda via AWS_NODEJS_CONNECTION_REUSE_ENABLED=1
    });

    const clientDuration = Date.now() - clientStartTime;
    const totalDuration = Date.now() - initStartTime;
    logger.info(`OpenSearch client instantiated in ${clientDuration}ms (total init: ${totalDuration}ms)`);
    return client;
  } catch (error) {
    logger.error('Error instantiating OpenSearch client:', error);
    throw error; // Re-throw
  }
}

/**
 * Gets a singleton instance of the OpenSearch client.
 * Handles concurrent calls during initialization.
 *
 * @returns {Promise<Client>} The OpenSearch client instance.
 */
async function getClient() {
  if (openSearchClientInstance) {
    logger.info('Returning cached OpenSearch client instance.');
    return openSearchClientInstance;
  }

  if (!clientInitializationPromise) {
    logger.info('No client instance or initialization promise found. Starting new initialization.');
    clientInitializationPromise = getClienInternal()
      .then(client => {
        openSearchClientInstance = client;
        logger.info('OpenSearch client successfully initialized and cached.');
        return client;
      })
      .catch(err => {
        logger.error('Failed to initialize OpenSearch client during promise execution:', err);
        clientInitializationPromise = null; // Reset promise to allow retry on subsequent calls
        throw err; // Propagate the error
      });
  } else {
    logger.info('OpenSearch client initialization already in progress. Awaiting existing promise.');
  }

  return clientInitializationPromise;
}

module.exports = {
  getClient,
};
