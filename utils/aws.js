const { SQSClient, SendMessageCommand } = require("@aws-sdk/client-sqs");
const { config } = require("../environment/index");
const { log: logger } = require("../utils/logger");

// Create AWS SDK v3 clients - let them use default credential resolution
const sqsClient = new SQSClient({
  region: "us-east-1",
  requestHandler: {
    connectionTimeout: 5000,
    socketTimeout: 30000,
    maxRetries: 2
  }
});

const S3_BUCKET = config.AWS.S3_BUCKET;
const { S3Client, GetObjectCommand } = require("@aws-sdk/client-s3");
const { Readable } = require("stream");

const s3Client = new S3Client({ region: config.AWS.S3_REGION }); // e.g., 'us-east-1'

const streamToString = async (stream) => {
  return new Promise((resolve, reject) => {
    const chunks = [];
    stream.on("data", (chunk) => chunks.push(chunk));
    stream.on("error", reject);
    stream.on("end", () => resolve(Buffer.concat(chunks).toString("utf-8")));
  });
};

const getDataFromS3 = async (key) => {
  const params = {
    Bucket: S3_BUCKET,
    Key: key,
  };

  try {
    const command = new GetObjectCommand(params);
    const data = await s3Client.send(command);
    const bodyContent = await streamToString(data.Body);
    const parsedData = JSON.parse(bodyContent);
    logger.info(`Data fetched successfully from S3 Path: ${key}`);
    return parsedData;
  } catch (error) {
    logger.error(`Error fetching data from S3: ${key} - ${error.message}`);
    return {};
  }
};

async function sendTargetComputationSQSMessage(message) {
  const command = new SendMessageCommand({
    MessageAttributes: {},
    MessageBody: message,
    QueueUrl: config.AWS.TARGET_COMPUTATION_SQS_URL,
    MessageGroupId: 'TargetComputation',
  });
  try {
    logger.info(`targetComputationSQSMessage: ${JSON.stringify(message)}`);
    const response = await sqsClient.send(command);
    logger.info(`response for targetComputationSQSMessage: ${JSON.stringify(response)}`);
    return true;
  } catch (err) {
    logger.info("Error sending message to Target Computation SQS", JSON.stringify(err));
    return false;
  }
}

module.exports = {
  getDataFromS3,
  sendTargetComputationSQSMessage,
};
